"""
Example usage of the ICD-11 multilingual terminology database system.
Demonstrates how to load CSV data and query the JSON schema.
"""

import logging
from csv_loader import CSVLoader
from json_schema import JSONSchemaManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Example usage of the ICD-11 multilingual terminology system."""
    
    print("=== ICD-11 Multilingual Terminology Database Example ===\n")
    
    try:
        # 1. Load CSV data into database
        print("1. Loading CSV data...")
        loader = CSVLoader()
        
        # Find and load ICD-11 CSV file
        csv_file = "icd11_multilingual_terms_normalized_6lang - icd11_multilingual_terms_normalized_6lang.csv"
        
        results = loader.load_csv_to_database(
            csv_file,
            create_table=True,
            drop_existing=True
        )
        
        if results['success']:
            print(f"✓ Loaded {results['records_inserted']} records successfully!")
        else:
            print("✗ Failed to load CSV data")
            for error in results['errors']:
                print(f"  Error: {error}")
            return
        
        # 2. Get database summary
        print("\n2. Database Summary:")
        summary = loader.get_loading_summary()
        print(f"   Total records: {summary.get('total_records', 'N/A')}")
        print(f"   Unique chapters: {summary.get('unique_chapters', 'N/A')}")
        print(f"   Unique terms: {summary.get('unique_terms', 'N/A')}")
        
        # 3. Example searches
        print("\n3. Example Searches:")
        
        # Search in English
        print("\n   English search for 'infection':")
        results = loader.search_loaded_terms('en', 'infection', 3)
        for i, result in enumerate(results, 1):
            terms = result['terms']
            en_term = terms.get('en', {}).get('raw', 'N/A')
            print(f"     {i}. Chapter {result['chapter_code']}, Term {result['term_code']}: {en_term}")
        
        # Search in Spanish
        print("\n   Spanish search for 'enfermedad':")
        results = loader.search_loaded_terms('es', 'enfermedad', 3)
        for i, result in enumerate(results, 1):
            terms = result['terms']
            es_term = terms.get('es', {}).get('raw', 'N/A')
            print(f"     {i}. Chapter {result['chapter_code']}, Term {result['term_code']}: {es_term}")
        
        # 4. Direct database operations
        print("\n4. Direct Database Operations:")
        
        # Get a specific term
        schema_manager = JSONSchemaManager()
        term = schema_manager.get_term(1, 588616678)  # Example chapter and term codes
        
        if term:
            print(f"\n   Retrieved term {term['term_code']} from chapter {term['chapter_code']}:")
            terms_data = term['terms']
            
            for lang, lang_data in terms_data.items():
                raw_text = lang_data.get('raw', '')
                normalized_text = lang_data.get('normalized', '')
                if raw_text:
                    print(f"     {lang.upper()}: {raw_text}")
                    if normalized_text and normalized_text != raw_text:
                        print(f"          (normalized: {normalized_text})")
        
        # 5. Insert a new term example
        print("\n5. Insert New Term Example:")
        
        new_terms = {
            "en": {
                "raw": "example medical condition",
                "normalized": "example medical condition"
            },
            "es": {
                "raw": "condición médica de ejemplo",
                "normalized": "condición médica ejemplo"
            }
        }
        
        success = schema_manager.insert_term(99, 999999, new_terms)
        if success:
            print("   ✓ Successfully inserted example term")
            
            # Retrieve the inserted term
            inserted_term = schema_manager.get_term(99, 999999)
            if inserted_term:
                print(f"   Retrieved inserted term: {inserted_term['terms']['en']['raw']}")
        else:
            print("   ✗ Failed to insert example term")
        
        # Close connections
        loader.close()
        schema_manager.close()
        
        print("\n=== Example completed successfully! ===")
        
    except Exception as e:
        print(f"Error in example: {e}")


if __name__ == "__main__":
    main()
