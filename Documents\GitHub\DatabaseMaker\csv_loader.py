"""
Simple CSV loader for ICD-11 multilingual terminology.
Loads CSV data and transforms it into JSON schema format for database storage.
"""

import pandas as pd
import logging
from typing import Dict, Any, Optional, List
from json_schema import JSONSchemaManager
import os

logger = logging.getLogger(__name__)


class CSVLoader:
    """
    Loads CSV files and transforms data into JSON schema format.
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize CSV loader with database connection."""
        self.schema_manager = JSONSchemaManager(db_config)
        self.supported_languages = ['en', 'es', 'fr', 'ar', 'de', 'uk']
    
    def load_csv_to_database(self, csv_file_path: str, 
                           create_table: bool = True,
                           drop_existing: bool = False) -> Dict[str, Any]:
        """
        Load CSV file into database using JSON schema.
        
        Args:
            csv_file_path: Path to CSV file
            create_table: Whether to create table if it doesn't exist
            drop_existing: Whether to drop existing table before creating
            
        Returns:
            Dictionary with loading results
        """
        results = {
            'success': False,
            'records_processed': 0,
            'records_inserted': 0,
            'errors': [],
            'file_path': csv_file_path
        }
        
        try:
            # Validate file exists
            if not os.path.exists(csv_file_path):
                raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
            
            # Create table if requested
            if create_table:
                logger.info("Creating database table...")
                if not self.schema_manager.create_table(drop_if_exists=drop_existing):
                    raise Exception("Failed to create database table")
                logger.info("✓ Database table ready")
            
            # Load and process CSV
            logger.info(f"Loading CSV file: {csv_file_path}")
            df = pd.read_csv(csv_file_path)
            
            # Validate CSV structure
            if not self._validate_csv_structure(df):
                raise ValueError("CSV file does not match expected structure")
            
            logger.info(f"Found {len(df)} records in CSV")
            results['records_processed'] = len(df)
            
            # Process each row
            inserted_count = 0
            for index, row in df.iterrows():
                try:
                    # Transform row to JSON schema format
                    chapter_code, term_code, terms = self._transform_row_to_json(row)
                    
                    # Insert into database
                    if self.schema_manager.insert_term(chapter_code, term_code, terms):
                        inserted_count += 1
                    else:
                        results['errors'].append(f"Failed to insert row {index + 1}")
                        
                except Exception as e:
                    error_msg = f"Error processing row {index + 1}: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            results['records_inserted'] = inserted_count
            results['success'] = inserted_count > 0
            
            # Log summary
            logger.info(f"✓ Processing complete:")
            logger.info(f"  Records processed: {results['records_processed']}")
            logger.info(f"  Records inserted: {results['records_inserted']}")
            logger.info(f"  Errors: {len(results['errors'])}")
            
            return results
            
        except Exception as e:
            error_msg = f"Failed to load CSV: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            return results
    
    def _validate_csv_structure(self, df: pd.DataFrame) -> bool:
        """Validate that CSV has expected columns."""
        required_columns = ['chapter_code', 'term_code']
        
        # Check for required columns
        for col in required_columns:
            if col not in df.columns:
                logger.error(f"Missing required column: {col}")
                return False
        
        # Check for language columns (at least some should exist)
        language_columns = []
        normalized_columns = []
        
        for lang in self.supported_languages:
            if lang in df.columns:
                language_columns.append(lang)
            
            normalized_col = f"{lang}_normalized"
            if normalized_col in df.columns:
                normalized_columns.append(normalized_col)
        
        if not language_columns:
            logger.error("No language columns found in CSV")
            return False
        
        logger.info(f"Found language columns: {language_columns}")
        logger.info(f"Found normalized columns: {normalized_columns}")
        
        return True
    
    def _transform_row_to_json(self, row: pd.Series) -> tuple:
        """
        Transform a CSV row into JSON schema format.

        Args:
            row: Pandas Series representing a CSV row

        Returns:
            Tuple of (chapter_code, term_code, terms_json)
        """
        # Extract chapter and term codes - handle non-integer values
        try:
            chapter_code = int(row['chapter_code'])
        except (ValueError, TypeError):
            # Handle non-integer chapter codes by converting to a hash or using a default
            chapter_str = str(row['chapter_code']).strip().lower()
            if chapter_str in ['other', 'unspecified']:
                chapter_code = 999  # Use 999 for 'other' and 'unspecified'
            elif chapter_str.startswith('v'):
                chapter_code = 900  # Use 900 for V codes
            elif chapter_str.startswith('x'):
                chapter_code = 800  # Use 800 for X codes
            else:
                # Use hash of the string for other non-integer values
                chapter_code = abs(hash(chapter_str)) % 1000

        try:
            term_code = int(row['term_code'])
        except (ValueError, TypeError):
            # Use hash of the term_code string if it's not an integer
            term_code = abs(hash(str(row['term_code']))) % 2147483647  # Max int value
        
        # Build terms JSON structure
        terms = {}
        
        for lang in self.supported_languages:
            lang_data = {}
            
            # Get raw text
            if lang in row and pd.notna(row[lang]) and str(row[lang]).strip():
                lang_data['raw'] = str(row[lang]).strip()
            else:
                lang_data['raw'] = ""
            
            # Get normalized text
            normalized_col = f"{lang}_normalized"
            if normalized_col in row and pd.notna(row[normalized_col]) and str(row[normalized_col]).strip():
                lang_data['normalized'] = str(row[normalized_col]).strip()
            else:
                lang_data['normalized'] = ""
            
            # Only include language if it has some content
            if lang_data['raw'] or lang_data['normalized']:
                terms[lang] = lang_data
        
        return chapter_code, term_code, terms
    
    def get_loading_summary(self) -> Dict[str, Any]:
        """Get summary of loaded data."""
        return self.schema_manager.get_table_stats()
    
    def search_loaded_terms(self, language: str, search_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search terms in loaded data."""
        return self.schema_manager.search_terms(language, search_text, limit)
    
    def close(self):
        """Close database connection."""
        self.schema_manager.close()


def find_icd11_csv_files(directory: str = ".") -> List[str]:
    """Find CSV files that start with 'icd11' in the given directory."""
    csv_files = []
    
    for file in os.listdir(directory):
        if file.lower().startswith('icd11') and file.lower().endswith('.csv'):
            csv_files.append(os.path.join(directory, file))
    
    return csv_files


def main():
    """Main function to load CSV data."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Find ICD-11 CSV files
        csv_files = find_icd11_csv_files()
        
        if not csv_files:
            print("No ICD-11 CSV files found (files starting with 'icd11')")
            return
        
        print(f"Found {len(csv_files)} ICD-11 CSV file(s):")
        for i, file in enumerate(csv_files, 1):
            print(f"  {i}. {file}")
        
        # Use the first file found, or let user choose
        if len(csv_files) == 1:
            csv_file = csv_files[0]
            print(f"\nUsing: {csv_file}")
        else:
            try:
                choice = int(input(f"\nChoose file (1-{len(csv_files)}): ")) - 1
                csv_file = csv_files[choice]
            except (ValueError, IndexError):
                csv_file = csv_files[0]
                print(f"Invalid choice, using: {csv_file}")
        
        # Initialize loader
        loader = CSVLoader()
        
        # Load CSV data
        print(f"\nLoading CSV data from: {csv_file}")
        results = loader.load_csv_to_database(
            csv_file, 
            create_table=True, 
            drop_existing=True
        )
        
        # Display results
        if results['success']:
            print("\n✓ CSV loading completed successfully!")
            print(f"  Records processed: {results['records_processed']}")
            print(f"  Records inserted: {results['records_inserted']}")
            
            # Show table summary
            summary = loader.get_loading_summary()
            print(f"\nDatabase Summary:")
            print(f"  Total records: {summary.get('total_records', 'N/A')}")
            print(f"  Unique chapters: {summary.get('unique_chapters', 'N/A')}")
            print(f"  Unique terms: {summary.get('unique_terms', 'N/A')}")
            
            # Example search
            print(f"\nExample search (English 'infection'):")
            search_results = loader.search_loaded_terms('en', 'infection', 3)
            for i, result in enumerate(search_results, 1):
                terms = result['terms']
                en_term = terms.get('en', {}).get('raw', 'N/A')
                print(f"  {i}. Chapter {result['chapter_code']}, Term {result['term_code']}: {en_term}")
        
        else:
            print("\n✗ CSV loading failed!")
            for error in results['errors']:
                print(f"  Error: {error}")
        
        loader.close()
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
