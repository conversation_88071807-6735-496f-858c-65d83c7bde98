"""
Improved normalized schema for ICD-11 multilingual terminology.
Provides better performance, scalability, and data integrity.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from database_connection import DatabaseConnection

logger = logging.getLogger(__name__)


class ImprovedSchemaManager:
    """
    Manages a properly normalized schema for ICD-11 multilingual terminology.
    
    Schema Design:
    - chapters: Reference table for ICD-11 chapters
    - languages: Reference table for supported languages  
    - terms: Core terms with metadata
    - term_translations: Multilingual translations (normalized)
    - term_search_index: Optimized search index with full-text search
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize schema manager with database connection."""
        self.db_connection = DatabaseConnection(db_config)
        
        # Ensure connection is established
        if not self.db_connection.connect():
            raise Exception("Failed to connect to database")
    
    def create_schema(self, drop_if_exists: bool = False) -> Dict[str, bool]:
        """
        Create the complete improved schema.
        
        Args:
            drop_if_exists: Whether to drop existing tables before creating
            
        Returns:
            Dictionary with creation status for each component
        """
        results = {}
        
        try:
            if drop_if_exists:
                self._drop_all_tables()
            
            # Create tables in dependency order
            results['languages'] = self._create_languages_table()
            results['chapters'] = self._create_chapters_table()
            results['terms'] = self._create_terms_table()
            results['term_translations'] = self._create_term_translations_table()
            results['term_search_index'] = self._create_search_index_table()
            
            # Create indexes and constraints
            results['indexes'] = self._create_indexes()
            results['constraints'] = self._create_constraints()
            
            # Create views for easy querying
            results['views'] = self._create_views()
            
            # Insert reference data
            results['reference_data'] = self._insert_reference_data()
            
            logger.info("Improved schema created successfully")
            return results
            
        except Exception as e:
            logger.error(f"Failed to create improved schema: {e}")
            return {'error': str(e)}
    
    def _drop_all_tables(self):
        """Drop all tables in correct order."""
        tables = [
            'term_search_index',
            'term_translations', 
            'terms',
            'chapters',
            'languages'
        ]
        
        for table in tables:
            drop_sql = f'DROP TABLE IF EXISTS "{table}" CASCADE;'
            self.db_connection.execute_command(drop_sql)
            logger.info(f"Dropped table: {table}")
    
    def _create_languages_table(self) -> bool:
        """Create languages reference table."""
        sql = """
        CREATE TABLE "languages" (
            "language_id" SERIAL PRIMARY KEY,
            "language_code" VARCHAR(3) NOT NULL UNIQUE,
            "language_name" VARCHAR(100) NOT NULL,
            "is_active" BOOLEAN DEFAULT TRUE
        );

        COMMENT ON TABLE "languages" IS 'Reference table for supported languages';
        COMMENT ON COLUMN "languages"."language_code" IS 'Short language code (en, es, fr, etc.)';
        """
        
        try:
            self.db_connection.execute_command(sql)
            logger.info("Created languages table")
            return True
        except Exception as e:
            logger.error(f"Failed to create languages table: {e}")
            return False
    
    def _create_chapters_table(self) -> bool:
        """Create chapters reference table."""
        sql = """
        CREATE TABLE "chapters" (
            "chapter_id" SERIAL PRIMARY KEY,
            "chapter_code" VARCHAR(10) NOT NULL UNIQUE,
            "chapter_number" INTEGER
        );
        
        COMMENT ON TABLE "chapters" IS 'ICD-11 chapters reference table';
        COMMENT ON COLUMN "chapters"."chapter_code" IS 'Original chapter code from CSV';
        COMMENT ON COLUMN "chapters"."chapter_number" IS 'Numeric chapter number if applicable';
        """
        
        try:
            self.db_connection.execute_command(sql)
            logger.info("Created chapters table")
            return True
        except Exception as e:
            logger.error(f"Failed to create chapters table: {e}")
            return False
    
    def _create_terms_table(self) -> bool:
        """Create main terms table."""
        sql = """
        CREATE TABLE "terms" (
            "term_id" SERIAL PRIMARY KEY,
            "term_code" VARCHAR(20) NOT NULL,
            "chapter_id" INTEGER NOT NULL REFERENCES "chapters"("chapter_id"),
            "is_active" BOOLEAN DEFAULT TRUE,
            UNIQUE("term_code", "chapter_id")
        );

        COMMENT ON TABLE "terms" IS 'Core terms with metadata';
        """
        
        try:
            self.db_connection.execute_command(sql)
            logger.info("Created terms table")
            return True
        except Exception as e:
            logger.error(f"Failed to create terms table: {e}")
            return False
    
    def _create_term_translations_table(self) -> bool:
        """Create term translations table."""
        sql = """
        CREATE TABLE "term_translations" (
            "translation_id" SERIAL PRIMARY KEY,
            "term_id" INTEGER NOT NULL REFERENCES "terms"("term_id") ON DELETE CASCADE,
            "language_id" INTEGER NOT NULL REFERENCES "languages"("language_id"),
            "raw_text" TEXT NOT NULL,
            "normalized_text" TEXT,
            "is_primary" BOOLEAN DEFAULT FALSE,
            "quality_score" DECIMAL(3,2) DEFAULT 1.0,
            UNIQUE("term_id", "language_id")
        );

        COMMENT ON TABLE "term_translations" IS 'Multilingual translations for terms';
        COMMENT ON COLUMN "term_translations"."is_primary" IS 'Whether this is the primary translation for this language';
        COMMENT ON COLUMN "term_translations"."quality_score" IS 'Translation quality score (0.0-1.0)';
        """
        
        try:
            self.db_connection.execute_command(sql)
            logger.info("Created term_translations table")
            return True
        except Exception as e:
            logger.error(f"Failed to create term_translations table: {e}")
            return False
    
    def _create_search_index_table(self) -> bool:
        """Create optimized search index table."""
        sql = """
        CREATE TABLE "term_search_index" (
            "search_id" SERIAL PRIMARY KEY,
            "term_id" INTEGER NOT NULL REFERENCES "terms"("term_id") ON DELETE CASCADE,
            "language_id" INTEGER NOT NULL REFERENCES "languages"("language_id"),
            "search_text" TEXT NOT NULL,
            "search_vector" TSVECTOR,
            "text_type" VARCHAR(20) DEFAULT 'raw',
            UNIQUE("term_id", "language_id", "text_type")
        );

        COMMENT ON TABLE "term_search_index" IS 'Optimized search index with full-text search vectors';
        COMMENT ON COLUMN "term_search_index"."text_type" IS 'Type of text (raw, normalized, stemmed)';
        COMMENT ON COLUMN "term_search_index"."search_vector" IS 'PostgreSQL full-text search vector';
        """
        
        try:
            self.db_connection.execute_command(sql)
            logger.info("Created term_search_index table")
            return True
        except Exception as e:
            logger.error(f"Failed to create term_search_index table: {e}")
            return False
    
    def _create_indexes(self) -> bool:
        """Create performance indexes."""
        indexes = [
            # Primary lookup indexes
            'CREATE INDEX "idx_terms_chapter_id" ON "terms"("chapter_id");',
            'CREATE INDEX "idx_terms_term_code" ON "terms"("term_code");',
            'CREATE INDEX "idx_terms_active" ON "terms"("is_active") WHERE "is_active" = TRUE;',
            
            # Translation indexes
            'CREATE INDEX "idx_translations_term_id" ON "term_translations"("term_id");',
            'CREATE INDEX "idx_translations_language_id" ON "term_translations"("language_id");',
            'CREATE INDEX "idx_translations_primary" ON "term_translations"("is_primary") WHERE "is_primary" = TRUE;',
            
            # Text search indexes
            'CREATE INDEX "idx_translations_raw_text" ON "term_translations" USING GIN(to_tsvector(\'english\', "raw_text"));',
            'CREATE INDEX "idx_translations_normalized_text" ON "term_translations" USING GIN(to_tsvector(\'english\', "normalized_text"));',
            
            # Search index table indexes
            'CREATE INDEX "idx_search_term_language" ON "term_search_index"("term_id", "language_id");',
            'CREATE INDEX "idx_search_vector" ON "term_search_index" USING GIN("search_vector");',
            'CREATE INDEX "idx_search_text" ON "term_search_index" USING GIN(to_tsvector(\'english\', "search_text"));',
            
            # Composite indexes for common queries
            'CREATE INDEX "idx_terms_chapter_active" ON "terms"("chapter_id", "is_active");',
            'CREATE INDEX "idx_translations_term_lang_primary" ON "term_translations"("term_id", "language_id", "is_primary");'
        ]
        
        try:
            for index_sql in indexes:
                self.db_connection.execute_command(index_sql)
            
            logger.info("Created performance indexes")
            return True
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            return False
    
    def _create_constraints(self) -> bool:
        """Create additional constraints."""
        constraints = [
            # Check constraints - only for columns that exist
            'ALTER TABLE "languages" ADD CONSTRAINT "chk_language_code_length" CHECK (LENGTH("language_code") BETWEEN 2 AND 3);'
        ]

        try:
            for constraint_sql in constraints:
                self.db_connection.execute_command(constraint_sql)

            logger.info("Created constraints")
            return True
        except Exception as e:
            logger.error(f"Failed to create constraints: {e}")
            return False

    def _create_views(self) -> bool:
        """Create convenient views for querying."""
        views = [
            # Complete term view with all translations
            '''
            CREATE OR REPLACE VIEW "v_terms_complete" AS
            SELECT
                t.term_id,
                t.term_code,
                c.chapter_code,
                l.language_code,
                l.language_name,
                tr.raw_text,
                tr.normalized_text,
                tr.is_primary,
                tr.quality_score
            FROM "terms" t
            JOIN "chapters" c ON t.chapter_id = c.chapter_id
            JOIN "term_translations" tr ON t.term_id = tr.term_id
            JOIN "languages" l ON tr.language_id = l.language_id
            WHERE l.is_active = TRUE AND t.is_active = TRUE;
            ''',

            # Summary view by chapter
            '''
            CREATE OR REPLACE VIEW "v_chapter_summary" AS
            SELECT
                c.chapter_id,
                c.chapter_code,
                COUNT(DISTINCT t.term_id) as total_terms,
                COUNT(DISTINCT tr.language_id) as languages_count,
                COUNT(tr.translation_id) as total_translations
            FROM "chapters" c
            LEFT JOIN "terms" t ON c.chapter_id = t.chapter_id
            LEFT JOIN "term_translations" tr ON t.term_id = tr.term_id
            GROUP BY c.chapter_id, c.chapter_code
            ORDER BY c.chapter_code;
            ''',

            # Search-optimized view
            '''
            CREATE OR REPLACE VIEW "v_search_ready" AS
            SELECT
                t.term_id,
                t.term_code,
                c.chapter_code,
                l.language_code,
                tr.raw_text,
                tr.normalized_text,
                si.search_vector
            FROM "terms" t
            JOIN "chapters" c ON t.chapter_id = c.chapter_id
            JOIN "term_translations" tr ON t.term_id = tr.term_id
            JOIN "languages" l ON tr.language_id = l.language_id
            LEFT JOIN "term_search_index" si ON t.term_id = si.term_id AND l.language_id = si.language_id
            WHERE l.is_active = TRUE;
            '''
        ]

        try:
            for view_sql in views:
                self.db_connection.execute_command(view_sql)

            logger.info("Created views")
            return True
        except Exception as e:
            logger.error(f"Failed to create views: {e}")
            return False

    def _insert_reference_data(self) -> bool:
        """Insert reference data for languages."""
        languages_data = [
            ('en', 'English'),
            ('es', 'Spanish'),
            ('fr', 'French'),
            ('ar', 'Arabic'),
            ('de', 'German'),
            ('uk', 'Ukrainian')
        ]

        try:
            for lang_code, lang_name in languages_data:
                insert_sql = '''
                INSERT INTO "languages" ("language_code", "language_name")
                VALUES (%s, %s)
                ON CONFLICT ("language_code") DO NOTHING;
                '''
                self.db_connection.execute_command(insert_sql, (lang_code, lang_name))

            logger.info("Inserted reference language data")
            return True
        except Exception as e:
            logger.error(f"Failed to insert reference data: {e}")
            return False

    def insert_term_data(self, chapter_code: str, term_code: str, translations: Dict[str, Dict[str, str]]) -> bool:
        """
        Insert a complete term with all translations.

        Args:
            chapter_code: Chapter code (will be created if doesn't exist)
            term_code: Term code
            translations: Dict of language_code -> {raw: str, normalized: str}
            icd11_code: Optional ICD-11 code
            term_type: Type of term

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure chapter exists
            chapter_id = self._ensure_chapter_exists(chapter_code)
            if not chapter_id:
                return False

            # Insert or update term
            term_sql = '''
            INSERT INTO "terms" ("term_code", "chapter_id")
            VALUES (%s, %s)
            ON CONFLICT ("term_code", "chapter_id")
            DO NOTHING
            RETURNING "term_id";
            '''

            result = self.db_connection.execute_query(term_sql, (term_code, chapter_id))
            if result:
                term_id = result[0]['term_id']
            else:
                # Term already exists, get its ID
                get_term_sql = 'SELECT "term_id" FROM "terms" WHERE "term_code" = %s AND "chapter_id" = %s;'
                result = self.db_connection.execute_query(get_term_sql, (term_code, chapter_id))
                if not result:
                    return False
                term_id = result[0]['term_id']

            # Insert translations
            for lang_code, texts in translations.items():
                if not texts.get('raw'):  # Skip empty translations
                    continue

                # Get language ID
                lang_result = self.db_connection.execute_query(
                    'SELECT "language_id" FROM "languages" WHERE "language_code" = %s;',
                    (lang_code,)
                )
                if not lang_result:
                    logger.warning(f"Language {lang_code} not found, skipping")
                    continue

                language_id = lang_result[0]['language_id']

                # Insert translation
                trans_sql = '''
                INSERT INTO "term_translations" ("term_id", "language_id", "raw_text", "normalized_text")
                VALUES (%s, %s, %s, %s)
                ON CONFLICT ("term_id", "language_id")
                DO UPDATE SET
                    "raw_text" = EXCLUDED."raw_text",
                    "normalized_text" = EXCLUDED."normalized_text";
                '''

                normalized_text = texts.get('normalized', texts.get('raw', ''))
                self.db_connection.execute_command(trans_sql, (term_id, language_id, texts['raw'], normalized_text))

                # Update search index
                self._update_search_index(term_id, language_id, texts['raw'], 'raw')
                if normalized_text and normalized_text != texts['raw']:
                    self._update_search_index(term_id, language_id, normalized_text, 'normalized')

            return True

        except Exception as e:
            logger.error(f"Failed to insert term data: {e}")
            return False

    def _ensure_chapter_exists(self, chapter_code: str) -> Optional[int]:
        """Ensure chapter exists and return its ID."""
        try:
            # Check if chapter exists
            result = self.db_connection.execute_query(
                'SELECT "chapter_id" FROM "chapters" WHERE "chapter_code" = %s;',
                (chapter_code,)
            )

            if result:
                return result[0]['chapter_id']

            # Create chapter
            insert_sql = '''
            INSERT INTO "chapters" ("chapter_code", "chapter_number")
            VALUES (%s, %s)
            RETURNING "chapter_id";
            '''

            # Try to extract numeric part
            chapter_number = None
            try:
                if chapter_code.isdigit():
                    chapter_number = int(chapter_code)
            except:
                pass

            result = self.db_connection.execute_query(insert_sql, (chapter_code, chapter_number))
            return result[0]['chapter_id'] if result else None

        except Exception as e:
            logger.error(f"Failed to ensure chapter exists: {e}")
            return None

    def _update_search_index(self, term_id: int, language_id: int, text: str, text_type: str):
        """Update search index for a term."""
        try:
            search_sql = '''
            INSERT INTO "term_search_index" ("term_id", "language_id", "search_text", "search_vector", "text_type")
            VALUES (%s, %s, %s, to_tsvector('english', %s), %s)
            ON CONFLICT ("term_id", "language_id", "text_type")
            DO UPDATE SET
                "search_text" = EXCLUDED."search_text",
                "search_vector" = EXCLUDED."search_vector";
            '''

            self.db_connection.execute_command(search_sql, (term_id, language_id, text, text, text_type))

        except Exception as e:
            logger.debug(f"Failed to update search index: {e}")

    def search_terms(self, search_text: str, language_code: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Advanced search with full-text search capabilities.

        Args:
            search_text: Text to search for
            language_code: Optional language filter
            limit: Maximum results

        Returns:
            List of matching terms with relevance ranking
        """
        try:
            if language_code:
                query = '''
                SELECT DISTINCT
                    v.term_id,
                    v.term_code,
                    v.chapter_code,
                    v.language_code,
                    v.raw_text,
                    v.normalized_text,
                    v.quality_score,
                    ts_rank(si.search_vector, plainto_tsquery('english', %s)) as relevance
                FROM "v_search_ready" v
                JOIN "term_search_index" si ON v.term_id = si.term_id
                WHERE v.language_code = %s
                AND (
                    si.search_vector @@ plainto_tsquery('english', %s)
                    OR v.raw_text ILIKE %s
                    OR v.normalized_text ILIKE %s
                )
                ORDER BY relevance DESC, v.quality_score DESC
                LIMIT %s;
                '''
                search_pattern = f"%{search_text}%"
                params = (search_text, language_code, search_text, search_pattern, search_pattern, limit)
            else:
                query = '''
                SELECT DISTINCT
                    v.term_id,
                    v.term_code,
                    v.chapter_code,
                    v.language_code,
                    v.raw_text,
                    v.normalized_text,
                    v.quality_score,
                    ts_rank(si.search_vector, plainto_tsquery('english', %s)) as relevance
                FROM "v_search_ready" v
                JOIN "term_search_index" si ON v.term_id = si.term_id
                WHERE (
                    si.search_vector @@ plainto_tsquery('english', %s)
                    OR v.raw_text ILIKE %s
                    OR v.normalized_text ILIKE %s
                )
                ORDER BY relevance DESC, v.quality_score DESC
                LIMIT %s;
                '''
                search_pattern = f"%{search_text}%"
                params = (search_text, search_text, search_pattern, search_pattern, limit)

            return self.db_connection.execute_query(query, params) or []

        except Exception as e:
            logger.error(f"Failed to search terms: {e}")
            return []

    def get_schema_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the schema."""
        try:
            stats_query = '''
            SELECT
                (SELECT COUNT(*) FROM "chapters") as total_chapters,
                (SELECT COUNT(*) FROM "languages" WHERE "is_active" = TRUE) as active_languages,
                (SELECT COUNT(*) FROM "terms") as total_terms,
                (SELECT COUNT(*) FROM "term_translations") as total_translations,
                (SELECT COUNT(*) FROM "term_search_index") as search_index_entries;
            '''

            result = self.db_connection.execute_query(stats_query)
            return result[0] if result else {}

        except Exception as e:
            logger.error(f"Failed to get schema stats: {e}")
            return {}

    def close(self):
        """Close database connection."""
        self.db_connection.disconnect()


def main():
    """Example usage of ImprovedSchemaManager."""
    try:
        # Initialize schema manager
        schema_manager = ImprovedSchemaManager()

        # Create schema
        print("Creating improved schema...")
        results = schema_manager.create_schema(drop_if_exists=True)

        if 'error' not in results:
            print("✓ Improved schema created successfully!")

            # Show schema stats
            stats = schema_manager.get_schema_stats()
            print(f"Schema stats: {stats}")
        else:
            print(f"✗ Failed to create schema: {results['error']}")

        schema_manager.close()

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
