# ICD-11 Multilingual Terminology Database

A simple Python tool for loading ICD-11 multilingual terminology CSV data into PostgreSQL using a JSON schema structure.

## Features

- **JSON Schema Storage**: Stores multilingual terms in a single table using PostgreSQL JSONB
- **Simple CSV Loading**: Direct loading of ICD-11 CSV files with automatic transformation
- **Multilingual Support**: Handles 6 languages (EN, ES, FR, AR, DE, UK) with raw and normalized text
- **Fast Search**: Optimized JSONB indexes for quick multilingual searches
- **Clean Architecture**: Minimal, focused codebase without unnecessary complexity

## Schema Structure

The database uses a single table with this JSON structure:

```json
{
  "chapter_code": "integer",
  "term_code": "integer",
  "terms": {
    "en": {
      "raw": "string",
      "normalized": "string"
    },
    "es": {
      "raw": "string",
      "normalized": "string"
    },
    "fr": {
      "raw": "string",
      "normalized": "string"
    },
    "ar": {
      "raw": "string",
      "normalized": "string"
    },
    "de": {
      "raw": "string",
      "normalized": "string"
    },
    "uk": {
      "raw": "string",
      "normalized": "string"
    }
  }
}
```

## Quick Start

1. **Install Dependencies**

   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Database**

   - Copy `.env.example` to `.env`
   - Update database connection details

3. **Load CSV Data**
   ```bash
   python csv_loader.py
   ```

## Usage Examples

### Load CSV Data

```python
from csv_loader import CSVLoader

# Initialize loader
loader = CSVLoader()

# Load ICD-11 CSV file
results = loader.load_csv_to_database(
    "icd11_multilingual_terms.csv",
    create_table=True,
    drop_existing=True
)

print(f"Loaded {results['records_inserted']} records")
```

### Search Terms

```python
from json_schema import JSONSchemaManager

# Initialize schema manager
schema = JSONSchemaManager()

# Search in English
results = schema.search_terms('en', 'infection', limit=10)
for result in results:
    print(f"Chapter {result['chapter_code']}: {result['terms']['en']['raw']}")

# Get specific term
term = schema.get_term(chapter_code=1, term_code=588616678)
print(term['terms']['en']['raw'])
```

### Insert New Terms

```python
# Add a new multilingual term
new_terms = {
    "en": {"raw": "new condition", "normalized": "new condition"},
    "es": {"raw": "nueva condición", "normalized": "nueva condición"}
}

success = schema.insert_term(99, 999999, new_terms)
```

## Configuration

Create a `.env` file with your PostgreSQL connection details:

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=Codex_TEST
DB_USER=postgres
DB_PASSWORD=Go123456
DB_SSLMODE=prefer
```

## CSV File Requirements

Your CSV files should:

- Start with 'icd11' prefix
- Have columns: `chapter_code`, `term_code`, language columns (`en`, `es`, `fr`, `ar`, `de`, `uk`)
- Optionally include normalized columns (`en_normalized`, `es_normalized`, etc.)

Example CSV structure:

```csv
chapter_code,term_code,en,es,fr,ar,de,uk,en_normalized,es_normalized,fr_normalized,ar_normalized,de_normalized,uk_normalized
1,588616678,gastroenteritis,gastroenteritis,gastroentérite,التهابات المعدة,,,gastroenteritis,gastroenteritis,gastroentérite,التهابات المعدة,,
```

## Project Structure

```
DatabaseMaker/
├── csv_loader.py          # Main CSV loading functionality
├── json_schema.py         # JSON schema table management
├── database_connection.py # Database connection handling
├── example.py            # Usage examples
├── requirements.txt      # Python dependencies
├── .env.example         # Environment configuration template
└── README.md            # This file
```

## Database Table

The system creates a single table `icd11_multilingual_terms` with:

- `id`: Serial primary key
- `chapter_code`: Integer (ICD-11 chapter)
- `term_code`: Integer (unique term identifier)
- `terms`: JSONB (multilingual terms structure)
- `created_at`: Timestamp
- `updated_at`: Timestamp

Indexes are automatically created for:

- Chapter and term codes
- JSONB content (GIN indexes)
- Individual language searches

## Performance

- **JSONB Storage**: Efficient binary JSON storage with fast queries
- **GIN Indexes**: Optimized for JSON searches across all languages
- **Unique Constraints**: Prevents duplicate terms
- **Batch Operations**: Efficient bulk loading of CSV data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with your ICD-11 CSV files
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Performance Features

- **Batch Processing**: Configurable batch sizes for memory efficiency
- **Sampling**: Analyzes subset of data for faster type detection on large files
- **Bulk Insert**: Uses efficient PostgreSQL bulk insert operations
- **Index Creation**: Creates indexes on unique/frequently queried columns

## Requirements

- Python 3.8+
- PostgreSQL 10+
- Required packages: pandas, psycopg2-binary, python-dotenv, sqlalchemy, numpy

## Files Structure

```
DatabaseMaker/
├── csv_to_db.py           # Main automation script
├── database_connection.py # Database connection management
├── csv_analyzer.py        # CSV analysis and type detection
├── table_creator.py       # Table creation logic
├── data_inserter.py       # Data insertion operations
├── example_usage.py       # Usage examples
├── example_data.csv       # Sample data file
├── requirements.txt       # Python dependencies
├── .env.example          # Environment configuration template
└── README.md             # This file
```
