"""
JSON Schema implementation for ICD-11 multilingual terminology.
Creates a single table that stores data in JSON format matching the provided schema.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from database_connection import DatabaseConnection

logger = logging.getLogger(__name__)


class JSONSchemaManager:
    """
    Manages a single table with JSON structure for ICD-11 multilingual terminology.
    
    Schema structure:
    {
        "chapter_code": "integer",
        "term_code": "integer", 
        "terms": {
            "en": {"raw": "string", "normalized": "string"},
            "es": {"raw": "string", "normalized": "string"},
            "fr": {"raw": "string", "normalized": "string"},
            "ar": {"raw": "string", "normalized": "string"},
            "de": {"raw": "string", "normalized": "string"},
            "uk": {"raw": "string", "normalized": "string"}
        }
    }
    """
    
    def __init__(self, db_config: Optional[Dict[str, str]] = None):
        """Initialize schema manager with database connection."""
        self.db_connection = DatabaseConnection(db_config)
        self.table_name = "icd11_multilingual_terms"
        
        # Ensure connection is established
        if not self.db_connection.connect():
            raise Exception("Failed to connect to database")
    
    def create_table(self, drop_if_exists: bool = False) -> bool:
        """
        Create the main table for storing ICD-11 multilingual terms in JSON format.
        
        Args:
            drop_if_exists: Whether to drop existing table before creating
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if drop_if_exists:
                drop_sql = f'DROP TABLE IF EXISTS "{self.table_name}" CASCADE;'
                self.db_connection.execute_command(drop_sql)
                logger.info(f"Dropped existing table: {self.table_name}")
            
            # Create table with JSON structure
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS "{self.table_name}" (
                "id" SERIAL PRIMARY KEY,
                "chapter_code" INTEGER NOT NULL,
                "term_code" INTEGER NOT NULL,
                "terms" JSONB NOT NULL,
                "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                -- Ensure unique combination of chapter_code and term_code
                UNIQUE("chapter_code", "term_code")
            );
            
            -- Create indexes for performance
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_chapter_code" 
                ON "{self.table_name}" ("chapter_code");
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_term_code" 
                ON "{self.table_name}" ("term_code");
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_gin" 
                ON "{self.table_name}" USING GIN ("terms");
            
            -- Create indexes for specific language searches
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_en" 
                ON "{self.table_name}" USING GIN ((terms->'en'));
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_es" 
                ON "{self.table_name}" USING GIN ((terms->'es'));
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_fr" 
                ON "{self.table_name}" USING GIN ((terms->'fr'));
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_ar" 
                ON "{self.table_name}" USING GIN ((terms->'ar'));
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_de" 
                ON "{self.table_name}" USING GIN ((terms->'de'));
            CREATE INDEX IF NOT EXISTS "idx_{self.table_name}_terms_uk" 
                ON "{self.table_name}" USING GIN ((terms->'uk'));
            
            -- Add comments for documentation
            COMMENT ON TABLE "{self.table_name}" IS 'ICD-11 multilingual terminology with JSON structure';
            COMMENT ON COLUMN "{self.table_name}"."chapter_code" IS 'ICD-11 chapter code';
            COMMENT ON COLUMN "{self.table_name}"."term_code" IS 'Unique term identifier';
            COMMENT ON COLUMN "{self.table_name}"."terms" IS 'JSON object containing multilingual terms and normalized versions';
            """
            
            success = self.db_connection.execute_command(create_sql)
            
            if success:
                logger.info(f"Successfully created table: {self.table_name}")
                self._log_table_info()
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to create table {self.table_name}: {e}")
            return False
    
    def insert_term(self, chapter_code: int, term_code: int, terms: Dict[str, Dict[str, str]]) -> bool:
        """
        Insert a single term record.
        
        Args:
            chapter_code: ICD-11 chapter code
            term_code: Unique term identifier
            terms: Dictionary containing language terms and normalized versions
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate terms structure
            if not self._validate_terms_structure(terms):
                logger.error("Invalid terms structure")
                return False
            
            insert_sql = f"""
            INSERT INTO "{self.table_name}" ("chapter_code", "term_code", "terms")
            VALUES (%s, %s, %s)
            ON CONFLICT ("chapter_code", "term_code") 
            DO UPDATE SET 
                "terms" = EXCLUDED."terms",
                "updated_at" = CURRENT_TIMESTAMP;
            """
            
            params = (chapter_code, term_code, json.dumps(terms))
            success = self.db_connection.execute_command(insert_sql, params)
            
            if success:
                logger.debug(f"Inserted/updated term: chapter={chapter_code}, term={term_code}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to insert term: {e}")
            return False
    
    def get_term(self, chapter_code: int, term_code: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve a specific term by chapter and term code.
        
        Args:
            chapter_code: ICD-11 chapter code
            term_code: Unique term identifier
            
        Returns:
            Term record as dictionary or None if not found
        """
        try:
            query = f"""
            SELECT "id", "chapter_code", "term_code", "terms", "created_at", "updated_at"
            FROM "{self.table_name}"
            WHERE "chapter_code" = %s AND "term_code" = %s;
            """
            
            result = self.db_connection.execute_query(query, (chapter_code, term_code))
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"Failed to retrieve term: {e}")
            return None
    
    def search_terms(self, language: str, search_text: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Search for terms in a specific language.
        
        Args:
            language: Language code (en, es, fr, ar, de, uk)
            search_text: Text to search for
            limit: Maximum number of results
            
        Returns:
            List of matching term records
        """
        try:
            # Search in both raw and normalized text
            query = f"""
            SELECT "id", "chapter_code", "term_code", "terms", "created_at", "updated_at"
            FROM "{self.table_name}"
            WHERE (
                "terms"->%s->>'raw' ILIKE %s OR 
                "terms"->%s->>'normalized' ILIKE %s
            )
            ORDER BY "chapter_code", "term_code"
            LIMIT %s;
            """
            
            search_pattern = f"%{search_text}%"
            params = (language, search_pattern, language, search_pattern, limit)
            
            return self.db_connection.execute_query(query, params) or []
            
        except Exception as e:
            logger.error(f"Failed to search terms: {e}")
            return []
    
    def get_table_stats(self) -> Dict[str, Any]:
        """Get statistics about the table."""
        try:
            stats_query = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT "chapter_code") as unique_chapters,
                COUNT(DISTINCT "term_code") as unique_terms,
                MIN("created_at") as earliest_record,
                MAX("updated_at") as latest_update
            FROM "{self.table_name}";
            """
            
            result = self.db_connection.execute_query(stats_query)
            return result[0] if result else {}
            
        except Exception as e:
            logger.error(f"Failed to get table stats: {e}")
            return {}
    
    def _validate_terms_structure(self, terms: Dict[str, Dict[str, str]]) -> bool:
        """Validate that terms follow the expected structure."""
        expected_languages = {'en', 'es', 'fr', 'ar', 'de', 'uk'}
        
        for lang in expected_languages:
            if lang in terms:
                if not isinstance(terms[lang], dict):
                    return False
                # Check for raw and normalized keys (allow missing values)
                if not all(key in ['raw', 'normalized'] for key in terms[lang].keys()):
                    return False
        
        return True
    
    def _log_table_info(self):
        """Log information about the created table."""
        try:
            info_query = f"""
            SELECT 
                column_name, 
                data_type, 
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = '{self.table_name}'
            ORDER BY ordinal_position;
            """
            
            columns = self.db_connection.execute_query(info_query)
            if columns:
                logger.info(f"Table {self.table_name} structure:")
                for col in columns:
                    logger.info(f"  {col['column_name']}: {col['data_type']} "
                              f"(nullable: {col['is_nullable']}, default: {col['column_default']})")
        
        except Exception as e:
            logger.debug(f"Could not log table info: {e}")
    
    def close(self):
        """Close database connection."""
        self.db_connection.disconnect()


def main():
    """Example usage of JSONSchemaManager."""
    try:
        # Initialize schema manager
        schema_manager = JSONSchemaManager()
        
        # Create table
        print("Creating table...")
        if schema_manager.create_table(drop_if_exists=True):
            print("✓ Table created successfully!")
            
            # Show table stats
            stats = schema_manager.get_table_stats()
            print(f"Table stats: {stats}")
        else:
            print("✗ Failed to create table")
        
        schema_manager.close()
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
